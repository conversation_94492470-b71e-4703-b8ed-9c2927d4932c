import os
import argparse
from ultralytics import YOLO
import logging
from datetime import datetime

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def parse_args():
    parser = argparse.ArgumentParser(description="YOLOv8 分割模型训练脚本")
    parser.add_argument('--data', type=str, default='extract_b/data.yaml', help='数据配置文件路径')
    parser.add_argument('--model', type=str, default='yolov8m-seg.pt', help='预训练模型路径')
    parser.add_argument('--epochs', type=int, default=150, help='训练轮数')
    parser.add_argument('--batch', type=int, default=16, help='批量大小')
    parser.add_argument('--imgsz', type=int, default=[480,640], help='输入图像尺寸')
    parser.add_argument('--device', type=str, default='0', help='训练设备 (例如 0 或 cpu)')
    parser.add_argument('--workers', type=int, default=0, help='数据加载线程数')
    parser.add_argument('--cache', action='store_true', help='缓存图像到内存')
    parser.add_argument('--project', type=str, default='runs/segment', help='保存训练结果的目录')
    parser.add_argument('--name', type=str, default='exp', help='实验名称')
    return parser.parse_args()

def main(): 
    args = parse_args()

    # 训练参数配置（针对小数据集优化）
    train_params = {
        'data': args.data,
        'model': args.model,
        'epochs': args.epochs,
        'batch': args.batch, 
        'imgsz': args.imgsz,
        'device': args.device,
        'workers': args.workers,
        'cache': args.cache,
        'project': args.project,
        'name': f"{args.name}_{datetime.now().strftime('%Y%m%d_%H%M%S')}",
        'optimizer': 'SGD',
        'patience': 30,  # 提前停止
        'lr0': 0.001,
        'lrf': 0.01,
        'warmup_epochs': 3,  # 预热轮数
        'weight_decay': 0.005,
        'hsv_h': 0.015,
        'hsv_s': 0.7,
        'hsv_v': 0.4,
        'degrees': 30.0,
        'translate': 0.1,
        'scale': 0.5,
        'shear': 0.0,
        'perspective': 0.0,
        'flipud': 0.5,
        'fliplr': 0.5,
        'mosaic': 0.8,
        'mixup': 0.0,
        'copy_paste': 0.1,
        'box': 7.5,
        'cls': 0.5,
        'dfl': 1.5,  # 使用 dfl 调整损失权重，替换无效的 seg 参数
        'pretrained': True,
        'verbose': True
    }

    # 记录训练参数
    logger.info("训练参数：")
    for key, value in train_params.items():
        logger.info(f"  {key}: {value}")

    # 加载 YOLOv8 模型
    logger.info(f"加载模型: {train_params['model']}")
    model = YOLO(train_params['model'])

    # 开始训练
    logger.info("开始训练...")
    results = model.train(**train_params)

    # 记录训练结果
    logger.info("训练完成！")
    logger.info(f"结果保存在: {os.path.join(train_params['project'], train_params['name'])}")

    # 验证模型
    logger.info("运行验证...")
    val_results = model.val(data=train_params['data'])
    logger.info(f"验证 mAP50: {val_results.box.map50:.4f}")
    logger.info(f"验证 mAP50-95: {val_results.box.map:.4f}")

if __name__ == '__main__':
    main()